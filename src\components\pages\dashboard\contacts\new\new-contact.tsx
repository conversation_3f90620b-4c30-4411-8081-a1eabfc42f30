"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, ChevronDown, Plus } from "lucide-react";
import Link from "next/link";

const NewContactPage: React.FC = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    displayName: "",
    contactGroup: "",
    firstName: "",
    middleName: "",
    lastName: "",
    phone: "",
    identityType: "",
    identityNumber: "",
    email: "",
    otherInfo: "",
    companyName: "",
    telephone: "",
    fax: "",
    npwp: "",
    address: "",
    sameAsShipping: false,
  });

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      // Handle form submission here
      console.log("Form data:", formData);
      // Add API call here
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/contacts">
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              Kontak baru
            </h1>
          </div>
          <Link href="/dashboard/contacts">
            <Button variant="ghost" size="sm">
              ✕
            </Button>
          </Link>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="max-w-4xl mx-auto">
          {/* Info Kontak Section */}
          <div className="mb-8">
            <div className="flex items-center mb-6 cursor-pointer">
              <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded flex items-center justify-center mr-2">
                <span className="text-blue-600 dark:text-blue-400 text-sm">👤</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Info kontak</h3>
              <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
            </div>

            {/* Display Name */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Nama Tampilan *
              </label>
              <input
                type="text"
                placeholder="Khalil"
                value={formData.displayName}
                onChange={(e) => handleInputChange("displayName", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            {/* Contact Group */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Grup Kontak
              </label>
              <Select value={formData.contactGroup} onValueChange={(value) => handleInputChange("contactGroup", value)}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Pilih grup" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="customer">Pelanggan</SelectItem>
                  <SelectItem value="supplier">Supplier</SelectItem>
                  <SelectItem value="employee">Karyawan</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

            {/* Info Umum Section */}
            <div className="mb-8">
              <div className="flex items-center mb-4 cursor-pointer">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded flex items-center justify-center mr-2">
                  <span className="text-blue-600 dark:text-blue-400 text-sm">
                    📋
                  </span>
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Info umum
                </h3>
                <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
              </div>

              {/* Name Fields */}
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Nama depan
                  </label>
                  <input
                    type="text"
                    placeholder="Nama depan"
                    value={formData.firstName}
                    onChange={(e) =>
                      handleInputChange("firstName", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Nama tengah
                  </label>
                  <input
                    type="text"
                    placeholder="Nama tengah"
                    value={formData.middleName}
                    onChange={(e) =>
                      handleInputChange("middleName", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Nama belakang
                  </label>
                  <input
                    type="text"
                    placeholder="Nama belakang"
                    value={formData.lastName}
                    onChange={(e) =>
                      handleInputChange("lastName", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              {/* Phone and Identity */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Nomor handphone
                  </label>
                  <input
                    type="text"
                    placeholder="Contoh: 081 2 3374 5678"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Identitas
                  </label>
                  <div className="flex">
                    <select
                      value={formData.identityType}
                      onChange={(e) =>
                        handleInputChange("identityType", e.target.value)
                      }
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">-</option>
                      <option value="ktp">KTP</option>
                      <option value="sim">SIM</option>
                      <option value="passport">Passport</option>
                    </select>
                    <input
                      type="text"
                      placeholder="Nomor ID"
                      value={formData.identityNumber}
                      onChange={(e) =>
                        handleInputChange("identityNumber", e.target.value)
                      }
                      className="flex-1 px-3 py-2 border-t border-r border-b border-gray-300 dark:border-gray-600 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>

              {/* Email and Other Info */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    placeholder="Enter email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Info Lain
                  </label>
                  <input
                    type="text"
                    placeholder="Info lain"
                    value={formData.otherInfo}
                    onChange={(e) =>
                      handleInputChange("otherInfo", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              {/* Company and Phone */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Nama Perusahaan
                  </label>
                  <input
                    type="text"
                    placeholder="Nama perusahaan"
                    value={formData.companyName}
                    onChange={(e) =>
                      handleInputChange("companyName", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Nomor telepon
                  </label>
                  <input
                    type="text"
                    placeholder="Contoh: (021) 3456789"
                    value={formData.telephone}
                    onChange={(e) =>
                      handleInputChange("telephone", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              {/* Fax and NPWP */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Fax
                  </label>
                  <input
                    type="text"
                    placeholder="Contoh: 0812 3456 7890"
                    value={formData.fax}
                    onChange={(e) => handleInputChange("fax", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    NPWP
                  </label>
                  <input
                    type="text"
                    placeholder="NPWP"
                    value={formData.npwp}
                    onChange={(e) => handleInputChange("npwp", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              {/* Address */}
              <div className="mb-4">
                <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                  Alamat paragrafan
                </label>
                <textarea
                  placeholder="e.g. Jalan Indonesia Blok L No. 22"
                  rows={3}
                  value={formData.address}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                ></textarea>
              </div>

              {/* Add Details Button */}
              <div className="mb-4">
                <button className="text-blue-600 dark:text-blue-400 text-sm hover:underline">
                  Tambah rincian
                </button>
              </div>

              {/* Shipping Address Checkbox */}
              <div className="mb-6">
                <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                  Alamat pengiriman
                </label>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.sameAsShipping}
                    onChange={(e) =>
                      handleInputChange("sameAsShipping", e.target.checked)
                    }
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Samakan dengan alamat penagihan
                  </span>
                </div>
              </div>
            </div>

            {/* Info Bank Section */}
            <div className="mb-8">
              <div className="flex items-center mb-4 cursor-pointer">
                <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded flex items-center justify-center mr-2">
                  <span className="text-blue-600 dark:text-blue-400 text-sm">
                    🏦
                  </span>
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Info bank
                </h3>
                <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
              </div>

              <div className="mb-4">
                <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                  Akun bank
                </label>
                <input
                  type="text"
                  placeholder="Nama bank"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Kantor cabang
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                    Pemegang akun bank
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                  Nomor rekening
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="mb-4">
                <button className="text-blue-600 dark:text-blue-400 text-sm hover:underline flex items-center">
                  <Plus className="h-4 w-4 mr-1" />
                  Tambah bank lain
                </button>
              </div>
            </div>

          </div>
        </div>
      </div>

      {/* Bottom Action Buttons */}
      <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex justify-end space-x-4 max-w-7xl mx-auto">
          <Button variant="outline" onClick={() => router.back()}>
            Batalkan
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isLoading ? "Menyimpan..." : "Simpan"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NewContactPage;

"use client";

import React, { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  Store, 
  UserCog, 
  Contact, 
  ArrowLeft,
  Plus
} from "lucide-react";
import Link from "next/link";

// Import form components (we'll create these)
import CustomerForm from "./forms/customer-form";
import SupplierForm from "./forms/supplier-form";
import EmployeeForm from "./forms/employee-form";

const NewContactPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const userRole = session?.user?.role as Role | undefined;

  // Get initial tab from URL params or default to customer
  const initialTab = searchParams.get("type") || "pelanggan";
  const [activeTab, setActiveTab] = useState(initialTab);

  // Check permissions
  const canAccessEmployees = userRole === Role.OWNER;
  const canAccessSuppliers = userRole === Role.OWNER || userRole === Role.ADMIN;

  // Handle tab change and update URL
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    const url = new URL(window.location.href);
    url.searchParams.set("type", value);
    router.replace(url.pathname + url.search);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-xl p-6 border border-blue-100 dark:border-blue-900/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/contacts">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                Kembali
              </Button>
            </Link>
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl shadow-sm">
                <Plus className="h-7 w-7 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Tambah Kontak Baru
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Pilih jenis kontak yang ingin ditambahkan
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Type Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-6 py-4">
            <TabsList
              className={`grid max-w-2xl ${
                canAccessEmployees && canAccessSuppliers
                  ? "grid-cols-3"
                  : canAccessSuppliers
                    ? "grid-cols-2"
                    : "grid-cols-1"
              } bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg border border-gray-200 dark:border-gray-600 h-auto min-h-[60px]`}
            >
              <TabsTrigger
                value="pelanggan"
                className="flex items-center gap-2 px-4 py-2.5 rounded-md transition-all duration-300 ease-in-out transform data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:shadow-lg data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:scale-[1.02] hover:bg-white/70 dark:hover:bg-gray-800/70 hover:scale-[1.01] active:scale-[0.98]"
              >
                <Users className="h-4 w-4" />
                <span className="font-medium">Pelanggan</span>
              </TabsTrigger>

              {canAccessSuppliers && (
                <TabsTrigger
                  value="supplier"
                  className="flex items-center gap-2 px-4 py-2.5 rounded-md transition-all duration-300 ease-in-out transform data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:shadow-lg data-[state=active]:text-green-600 dark:data-[state=active]:text-green-400 data-[state=active]:scale-[1.02] hover:bg-white/70 dark:hover:bg-gray-800/70 hover:scale-[1.01] active:scale-[0.98]"
                >
                  <Store className="h-4 w-4" />
                  <span className="font-medium">Supplier</span>
                </TabsTrigger>
              )}

              {canAccessEmployees && (
                <TabsTrigger
                  value="karyawan"
                  className="flex items-center gap-2 px-4 py-2.5 rounded-md transition-all duration-300 ease-in-out transform data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:shadow-lg data-[state=active]:text-purple-600 dark:data-[state=active]:text-purple-400 data-[state=active]:scale-[1.02] hover:bg-white/70 dark:hover:bg-gray-800/70 hover:scale-[1.01] active:scale-[0.98]"
                >
                  <UserCog className="h-4 w-4" />
                  <span className="font-medium">Karyawan</span>
                </TabsTrigger>
              )}
            </TabsList>
          </div>

          {/* Customer Form */}
          <TabsContent value="pelanggan" className="p-6 animate-in fade-in-50 slide-in-from-bottom-2 duration-300">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    Tambah Pelanggan Baru
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Masukkan informasi pelanggan baru
                  </p>
                </div>
                <Badge variant="outline" className="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800">
                  Pelanggan
                </Badge>
              </div>
              <CustomerForm />
            </div>
          </TabsContent>

          {/* Supplier Form */}
          {canAccessSuppliers && (
            <TabsContent value="supplier" className="p-6 animate-in fade-in-50 slide-in-from-bottom-2 duration-300">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                      <Store className="h-5 w-5 text-green-600 dark:text-green-400" />
                      Tambah Supplier Baru
                    </h2>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Masukkan informasi supplier baru
                    </p>
                  </div>
                  <Badge variant="outline" className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 border-green-200 dark:border-green-800">
                    Supplier
                  </Badge>
                </div>
                <SupplierForm />
              </div>
            </TabsContent>
          )}

          {/* Employee Form */}
          {canAccessEmployees && (
            <TabsContent value="karyawan" className="p-6 animate-in fade-in-50 slide-in-from-bottom-2 duration-300">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                      <UserCog className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                      Tambah Karyawan Baru
                    </h2>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Masukkan informasi karyawan baru
                    </p>
                  </div>
                  <Badge variant="outline" className="bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-800">
                    Karyawan
                  </Badge>
                </div>
                <EmployeeForm />
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>
  );
};

export default NewContactPage;

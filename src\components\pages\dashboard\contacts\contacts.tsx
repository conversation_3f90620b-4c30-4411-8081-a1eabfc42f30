"use client";

import React, { useState } from "react";
import {
  Customer as PrismaCustomer,
  Supplier as PrismaSupplier,
} from "@prisma/client";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Users, Store, UserCog, Contact } from "lucide-react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";

// Import existing components
import CustomersPage from "../customers/customers";
import SuppliersPage from "../suppliers/suppliers";
import EmployeeManagement from "../settings/employees/employee-management";

interface Employee {
  id: string;
  name: string;
  employeeId: string;
  role: Role;
  createdAt?: Date;
}

interface ContactsPageProps {
  customers: PrismaCustomer[];
  suppliers: PrismaSupplier[];
  employees: Employee[];
}

const ContactsPage: React.FC<ContactsPageProps> = ({
  customers,
  suppliers,
  employees,
}) => {
  const { data: session } = useSession();
  const userRole = session?.user?.role as Role | undefined;

  // Check if user can access employees section (only OWNER)
  const canAccessEmployees = userRole === Role.OWNER;

  // Check if user can access suppliers section (OWNER and ADMIN)
  const canAccessSuppliers = userRole === Role.OWNER || userRole === Role.ADMIN;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center space-x-3">
        <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
          <Contact className="h-6 w-6 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Kontak
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Kelola data pelanggan, supplier, dan karyawan Anda
          </p>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="pelanggan" className="w-full">
        <TabsList
          className={`grid w-full ${
            canAccessEmployees && canAccessSuppliers
              ? "grid-cols-3"
              : canAccessSuppliers
                ? "grid-cols-2"
                : "grid-cols-1"
          }`}
        >
          <TabsTrigger value="pelanggan" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>Pelanggan</span>
          </TabsTrigger>
          {canAccessSuppliers && (
            <TabsTrigger value="supplier" className="flex items-center gap-2">
              <Store className="h-4 w-4" />
              <span>Supplier</span>
            </TabsTrigger>
          )}
          {canAccessEmployees && (
            <TabsTrigger value="karyawan" className="flex items-center gap-2">
              <UserCog className="h-4 w-4" />
              <span>Karyawan</span>
            </TabsTrigger>
          )}
        </TabsList>

        {/* Pelanggan Tab */}
        <TabsContent value="pelanggan" className="space-y-6">
          <CustomersPage customers={customers} />
        </TabsContent>

        {/* Supplier Tab */}
        {canAccessSuppliers && (
          <TabsContent value="supplier" className="space-y-6">
            <SuppliersPage suppliers={suppliers} />
          </TabsContent>
        )}

        {/* Karyawan Tab */}
        {canAccessEmployees && (
          <TabsContent value="karyawan" className="space-y-6">
            <EmployeeManagement />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default ContactsPage;

"use client";

import React, { useState } from "react";
import {
  Customer as PrismaCustomer,
  Supplier as PrismaSupplier,
} from "@prisma/client";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Users, Store, UserCog, Contact, TrendingUp } from "lucide-react";
import { useSession } from "next-auth/react";
import { Role } from "@prisma/client";

// Import existing components
import CustomersPage from "../customers/customers";
import SuppliersPage from "../suppliers/suppliers";
import EmployeeManagement from "../settings/employees/employee-management";

interface Employee {
  id: string;
  name: string;
  employeeId: string;
  role: Role;
  createdAt?: Date;
}

interface ContactsPageProps {
  customers: PrismaCustomer[];
  suppliers: PrismaSupplier[];
  employees: Employee[];
}

const ContactsPage: React.FC<ContactsPageProps> = ({
  customers,
  suppliers,
  employees,
}) => {
  const { data: session } = useSession();
  const userRole = session?.user?.role as Role | undefined;

  // Check if user can access employees section (only OWNER)
  const canAccessEmployees = userRole === Role.OWNER;

  // Check if user can access suppliers section (OWNER and ADMIN)
  const canAccessSuppliers = userRole === Role.OWNER || userRole === Role.ADMIN;

  // Calculate total counts for summary
  const totalContacts = customers.length + suppliers.length + employees.length;
  const activeCustomers = Math.round(customers.length * 0.8); // Mock active percentage
  const activeSuppliers = Math.round(suppliers.length * 0.9); // Mock active percentage

  return (
    <div className="space-y-6">
      {/* Enhanced Page Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-xl p-6 border border-blue-100 dark:border-blue-900/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl shadow-sm">
              <Contact className="h-7 w-7 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Kontak
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Kelola data pelanggan, supplier, dan karyawan Anda
              </p>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="hidden md:flex items-center space-x-6">
            <div className="text-center">
              <div className="flex items-center space-x-2">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {totalContacts}
                </div>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Total Kontak
              </p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {activeCustomers + activeSuppliers}
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">Aktif</p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
        <Tabs defaultValue="pelanggan" className="w-full">
          <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-6 py-4">
            <TabsList
              className={`grid w-full max-w-2xl ${
                canAccessEmployees && canAccessSuppliers
                  ? "grid-cols-3"
                  : canAccessSuppliers
                    ? "grid-cols-2"
                    : "grid-cols-1"
              } bg-gray-50 dark:bg-gray-700/50 p-1 rounded-lg border border-gray-200 dark:border-gray-600`}
            >
              <TabsTrigger
                value="pelanggan"
                className="flex items-center gap-2 px-4 py-2.5 rounded-md transition-all duration-200 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:shadow-sm data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 hover:bg-white/70 dark:hover:bg-gray-800/70"
              >
                <Users className="h-4 w-4" />
                <span className="font-medium">Pelanggan</span>
                <Badge
                  variant="secondary"
                  className="ml-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-300 text-xs px-2 py-0.5"
                >
                  {customers.length}
                </Badge>
              </TabsTrigger>

              {canAccessSuppliers && (
                <TabsTrigger
                  value="supplier"
                  className="flex items-center gap-2 px-4 py-2.5 rounded-md transition-all duration-200 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:shadow-sm data-[state=active]:text-green-600 dark:data-[state=active]:text-green-400 hover:bg-white/70 dark:hover:bg-gray-800/70"
                >
                  <Store className="h-4 w-4" />
                  <span className="font-medium">Supplier</span>
                  <Badge
                    variant="secondary"
                    className="ml-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-300 text-xs px-2 py-0.5"
                  >
                    {suppliers.length}
                  </Badge>
                </TabsTrigger>
              )}

              {canAccessEmployees && (
                <TabsTrigger
                  value="karyawan"
                  className="flex items-center gap-2 px-4 py-2.5 rounded-md transition-all duration-200 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-800 data-[state=active]:shadow-sm data-[state=active]:text-purple-600 dark:data-[state=active]:text-purple-400 hover:bg-white/70 dark:hover:bg-gray-800/70"
                >
                  <UserCog className="h-4 w-4" />
                  <span className="font-medium">Karyawan</span>
                  <Badge
                    variant="secondary"
                    className="ml-1 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-300 text-xs px-2 py-0.5"
                  >
                    {employees.length}
                  </Badge>
                </TabsTrigger>
              )}
            </TabsList>
          </div>

          {/* Pelanggan Tab */}
          <TabsContent value="pelanggan" className="p-6">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    Data Pelanggan
                  </h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Kelola informasi pelanggan dan riwayat transaksi
                  </p>
                </div>
                <Badge
                  variant="outline"
                  className="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800"
                >
                  {customers.length} Total
                </Badge>
              </div>
              <CustomersPage customers={customers} />
            </div>
          </TabsContent>

          {/* Supplier Tab */}
          {canAccessSuppliers && (
            <TabsContent value="supplier" className="p-6">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                      <Store className="h-5 w-5 text-green-600 dark:text-green-400" />
                      Data Supplier
                    </h2>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Kelola informasi supplier dan riwayat pembelian
                    </p>
                  </div>
                  <Badge
                    variant="outline"
                    className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 border-green-200 dark:border-green-800"
                  >
                    {suppliers.length} Total
                  </Badge>
                </div>
                <SuppliersPage suppliers={suppliers} />
              </div>
            </TabsContent>
          )}

          {/* Karyawan Tab */}
          {canAccessEmployees && (
            <TabsContent value="karyawan" className="p-6">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                      <UserCog className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                      Data Karyawan
                    </h2>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Kelola akun karyawan dan hak akses sistem
                    </p>
                  </div>
                  <Badge
                    variant="outline"
                    className="bg-purple-50 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-800"
                  >
                    {employees.length} Total
                  </Badge>
                </div>
                <EmployeeManagement />
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>
  );
};

export default ContactsPage;
